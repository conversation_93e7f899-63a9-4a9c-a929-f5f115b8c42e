package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.vo2MaxZone1
import com.stt.android.compose.theme.vo2MaxZone2
import com.stt.android.compose.theme.vo2MaxZone3
import com.stt.android.domain.diary.Vo2MaxRange
import com.stt.android.domain.diary.Vo2MaxState
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.vo2MaxMonthDayLabel
import com.stt.android.home.dashboardv2.widgets.Vo2MaxRangeItem
import com.stt.android.home.dashboardv2.widgets.Vo2MaxWidgetInfo
import com.stt.android.home.dashboardv2.widgets.labelResId
import com.stt.android.home.dashboardv2.widgets.offsetOfCanvas
import com.stt.android.ui.utils.TextFormatter
import java.time.Year
import kotlin.math.ceil

@Composable
internal fun Vo2MaxWidget(
    widgetInfo: Vo2MaxWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val (subHeader, title, subtitle) = if (widgetInfo.latestVo2Max == null) {
        Triple(
            stringResource(R.string.widget_no_data_title),
            stringResource(R.string.widget_no_data_title),
            stringResource(R.string.widget_no_data_subtitle),
        )
    } else {
        Triple(
            widgetInfo.latestVo2MaxDate?.let { latestVo2MaxDate ->
                val vo2MaxLocaleDate = latestVo2MaxDate.toLocalDate()
                if (vo2MaxLocaleDate.year == Year.now().value) {
                    vo2MaxLocaleDate.vo2MaxMonthDayLabel
                } else {
                    TextFormatter.formatDate(context, latestVo2MaxDate, true)
                }
            }.orEmpty(),
            TextFormatter.formatVo2Max(widgetInfo.latestVo2Max),
            widgetInfo.state?.let { stringResource(it.labelResId) }.orEmpty(),
        )
    }
    CommonChartWidget(
        editMode = editMode,
        headerRes = R.string.dashboard_widget_title_max_vo2,
        iconRes = R.drawable.ic_dashboard_widget_max_vo2,
        colorRes = R.color.dashboard_widget_max_vo2,
        subheaderText = subHeader,
        titleText = AnnotatedString(title),
        subtitleText = subtitle,
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
    ) {
        Vo2MaxProgress(widgetInfo = widgetInfo)
    }
}

@Composable
internal fun Vo2MaxProgress(
    widgetInfo: Vo2MaxWidgetInfo,
    modifier: Modifier = Modifier,
) {
    val rangeItemList = widgetInfo.rangeItemList
    val currentVo2 = widgetInfo.latestVo2Max

    // 统一计算范围值，确保 ProgressBar 和 Marker 使用相同的 minValue 和 maxValue
    val (rangeItems, minValue, maxValue) = remember(currentVo2, rangeItemList) {
        val processedRangeItems = if (currentVo2 != null) {
            rangeItemList
        } else {
            if (rangeItemList.size > 2) {
                val trimmed = rangeItemList.drop(1).dropLast(1)
                trimmed.mapIndexed { index, rangeItem ->
                    val newMin = if (index == 0) rangeItemList.first().maxValue else trimmed[index].minValue
                    rangeItem.copy(minValue = newMin)
                }
            } else {
                emptyList()
            }
        }

        val min = if (processedRangeItems.isNotEmpty()) processedRangeItems.first().minValue.toFloat() else 0f
        val max = if (processedRangeItems.isNotEmpty()) processedRangeItems.last().maxValue.toFloat() else 100f

        Triple(processedRangeItems, min, max)
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Vo2MaxProgressBar(
            currentVo2 = currentVo2,
            vo2MaxState = widgetInfo.state,
            rangeItems = rangeItems,
            minValue = minValue,
            maxValue = maxValue,
        )

        Spacer(
            modifier = Modifier
                .height(MaterialTheme.spacing.xsmall)
                .fillMaxWidth()
        )

        Vo2MaxMarker(
            currentVo2 = currentVo2,
            rangeItemList = rangeItemList,
            minValue = minValue,
            maxValue = maxValue,
        )
    }
}

@Composable
internal fun Vo2MaxProgressBar(
    currentVo2: Float?,
    vo2MaxState: Vo2MaxState?,
    rangeItems: List<Vo2MaxRange>,
    minValue: Float,
    maxValue: Float,
    modifier: Modifier = Modifier,
) {
    var indicatorOffsetX by remember { mutableFloatStateOf(0f) }

    val zone1NormalColor = MaterialTheme.colorScheme.vo2MaxZone1
    val zone1EmptyColor = MaterialTheme.colorScheme.nearWhite
    val zone2NormalColor = MaterialTheme.colorScheme.vo2MaxZone2
    val zone2EmptyColor = MaterialTheme.colorScheme.lightGrey
    val zone3NormalColor = MaterialTheme.colorScheme.vo2MaxZone3
    val zone3EmptyColor = MaterialTheme.colorScheme.cloudyGrey

    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp)
                .clip(RoundedCornerShape(2.dp))
                .align(Alignment.Center)
        ) {
            val canvasWidth = size.width
            val canvasHeight = size.height
            var rangeOffsetX = 0f

            rangeItems.forEachIndexed { index, rangeItem ->
                val progressColor = when (rangeItem.state) {
                    Vo2MaxState.POOR,
                    Vo2MaxState.VERY_POOR -> if (currentVo2 == null)  zone1EmptyColor else zone1NormalColor

                    Vo2MaxState.GOOD,
                    Vo2MaxState.FAIR -> if (currentVo2 == null) zone2EmptyColor else zone2NormalColor

                    Vo2MaxState.SUPERIOR,
                    Vo2MaxState.EXCELLENT -> if (currentVo2 == null) zone3EmptyColor else zone3NormalColor
                }

                val rangeWidth = if (index == rangeItems.lastIndex) {
                    canvasWidth - rangeOffsetX
                } else {
                    canvasWidth * (rangeItems[index + 1].maxValue - rangeItem.maxValue) / (maxValue - minValue)
                }
                drawRect(
                    color = progressColor,
                    topLeft = Offset(rangeOffsetX, 0f),
                    size = Size(rangeWidth, canvasHeight),
                )
                rangeOffsetX += rangeWidth
            }
            if (currentVo2 != null) {
                indicatorOffsetX = canvasWidth * (currentVo2 - minValue) / (maxValue - minValue)
            }
        }

        if (currentVo2 != null && vo2MaxState != null) {
            IndicatorCursor(
                vo2MaxState = vo2MaxState,
                modifier = Modifier.offset {
                    IntOffset(x = indicatorOffsetX.toInt(), y = 0)
                }
            )
        }
    }
}

@Composable
internal fun Vo2MaxMarker(
    currentVo2: Float?,
    rangeItemList: List<Vo2MaxRange>,
    minValue: Float,
    maxValue: Float,
    modifier: Modifier = Modifier,
) {
    val markerList = remember(currentVo2, rangeItemList) {
        if (currentVo2 == null) {
            rangeItemList.dropLast(1).map { it.maxValue }
        } else {
            rangeItemList.map { it.maxValue }.toMutableList().apply {
                this.add(0, rangeItemList.first().minValue)
            }
        }
    }

    val textMeasurer = rememberTextMeasurer()
    val gray = MaterialTheme.colorScheme.mediumGrey
    val textStyle = MaterialTheme.typography.bodySmall.merge(gray)
    val markerLineHeight = LocalDensity.current.run { 3.dp.toPx() }
    val markerLabelSpace = LocalDensity.current.run { 8.dp.toPx() }

    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(24.dp)
        ) {
            val canvasWidth = size.width
            markerList.forEachIndexed { index, marker ->
                val text = marker.toString()
                val textLayoutResult = textMeasurer.measure(text)
                val textSize = textLayoutResult.size
                val markerOffsetX = canvasWidth * (marker - minValue) / (maxValue - minValue)
                val lineOffset = Offset(x = markerOffsetX, y = 0f)
                val labelOffset = Offset(x = markerOffsetX, y = markerLineHeight + markerLabelSpace)

                if ((index != 0 && index != markerList.lastIndex) || currentVo2 == null) {
                    drawLine(
                        color = gray,
                        start = lineOffset,
                        end = lineOffset.copy(y = markerLineHeight),
                        strokeWidth = density,
                    )

                    drawText(
                        textMeasurer = textMeasurer,
                        style = textStyle,
                        text = text,
                        topLeft = when (index) {
                            0 -> labelOffset.copy(x = markerOffsetX)
                            markerList.lastIndex -> labelOffset.copy(x = canvasWidth - textSize.width)
                            else -> labelOffset.copy(x = markerOffsetX - textSize.width / 2f)
                        },
                    )
                }
            }
        }
    }
}

@Composable
internal fun IndicatorCursor(
    vo2MaxState: Vo2MaxState?,
    modifier: Modifier = Modifier
) {
    val cursorBgColor = when (vo2MaxState) {
        Vo2MaxState.POOR,
        Vo2MaxState.VERY_POOR -> MaterialTheme.colorScheme.vo2MaxZone1

        Vo2MaxState.GOOD,
        Vo2MaxState.FAIR -> MaterialTheme.colorScheme.vo2MaxZone2

        Vo2MaxState.SUPERIOR,
        Vo2MaxState.EXCELLENT -> MaterialTheme.colorScheme.vo2MaxZone3

        else -> MaterialTheme.colorScheme.primaryContainer
    }
    Box(
        modifier = modifier
            .size(5.dp, 18.dp)
            .border(
                width = MaterialTheme.spacing.xxxsmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                shape = RoundedCornerShape(2.dp),
            )
            .background(
                color = cursorBgColor,
                shape = RoundedCornerShape(2.dp),
            )
    )
}

/*@Preview(widthDp = 170, heightDp = 170)
@Preview(widthDp = 170, heightDp = 170, locale = "zh")
@Composable
private fun Vo2MaxWidgetPreview() {
    M3AppTheme {
        Vo2MaxWidget(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            widgetInfo = Vo2MaxWidgetInfo(
                latestVo2Max = 51f,
                latestVo2MaxDate = 1736245494239L,
                state = Vo2MaxState.GOOD,
                rangeItemList = listOf(
                    Vo2MaxRangeItem.Zone1RangeItem(0, 39),
                    Vo2MaxRangeItem.Zone2RangeItem(40, 51),
                    Vo2MaxRangeItem.Zone3RangeItem(52, 100),
                )
            ),
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {}
        )
    }
}

@Preview(widthDp = 170, heightDp = 170)
@Preview(widthDp = 170, heightDp = 170, locale = "zh")
@Composable
private fun Vo2MaxWidgetEmptyPreview() {
    M3AppTheme {
        Vo2MaxWidget(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            widgetInfo = Vo2MaxWidgetInfo(
                latestVo2Max = null,
                latestVo2MaxDate = 0L,
                state = Vo2MaxState.GOOD,
                rangeItemList = listOf(
                    Vo2MaxRangeItem.Zone1RangeItem(0, 37),
                    Vo2MaxRangeItem.Zone2RangeItem(37, 58),
                    Vo2MaxRangeItem.Zone3RangeItem(58, 100),
                )
            ),
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {}
        )
    }
}*/


